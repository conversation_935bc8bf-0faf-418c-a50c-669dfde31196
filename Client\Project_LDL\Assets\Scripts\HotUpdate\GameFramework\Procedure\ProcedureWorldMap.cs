using GameFramework.Fsm;
using GameFramework.Procedure;
using UnityEngine;
using UnityGameFramework.Runtime;

namespace Game.Hotfix
{
    public class ProcedureWorldMap : ProcedureBase
    {
        private IFsm<IProcedureManager> m_ProcedureOwner;
        public override bool UseNativeDialog { get; }

        private int? m_UIId = null;
        private int? m_LowUIId = null;

        public void GoBackToMain()
        {
            m_ProcedureOwner.SetData<VarInt32>("NextSceneId", (int)SceneDefine.MainScene);
            ChangeState<ProcedureChangeScene>(m_ProcedureOwner);
        }

        protected override void OnEnter(IFsm<IProcedureManager> procedureOwner)
        {
            base.OnEnter(procedureOwner);
            m_ProcedureOwner = procedureOwner;

            m_UIId = GameEntry.UI.OpenUIForm(EnumUIForm.UIMainFaceForm);
            m_LowUIId = GameEntry.UI.OpenUIForm(EnumUIForm.UIWorldMapPreviewLow);
            GameEntry.UI.OpenUIForm(EnumUIForm.UIClickEffectForm);

            // 检查UITechForm是否已经打开
            if (GameEntry.UI.HasUIForm((int)EnumUIForm.UITechForm))
            {
                // 获取UITechForm实例
                var techFormLogic = GameEntry.UI.GetUIForm(EnumUIForm.UITechForm);
                if (techFormLogic != null)
                {
                    GameEntry.UI.OpenUIForm(EnumUIForm.UITechForm);
                }
            }
        }

        protected override void OnLeave(IFsm<IProcedureManager> procedureOwner, bool isShutdown)
        {
            if (m_UIId != null && GameEntry.UI.HasUIForm(m_UIId.Value))
                GameEntry.UI.CloseUIForm(m_UIId.Value);
            if (m_LowUIId != null && GameEntry.UI.HasUIForm(m_LowUIId.Value))
                GameEntry.UI.CloseUIForm(m_LowUIId.Value);
            GameEntry.UI.CloseAllLoadedUIForms();

            base.OnLeave(procedureOwner, isShutdown);
        }

        public void GotoMainCity()
        {
            m_ProcedureOwner.SetData<VarInt32>("NextSceneId", (int)SceneDefine.MainScene);
            ChangeState<ProcedureChangeScene>(m_ProcedureOwner);
        }

        public void GotoBattle(int sceneId)
        {
            m_ProcedureOwner.SetData<VarInt32>("NextSceneId", sceneId);
            ChangeState<ProcedureChangeScene>(m_ProcedureOwner);
        }
    }
}