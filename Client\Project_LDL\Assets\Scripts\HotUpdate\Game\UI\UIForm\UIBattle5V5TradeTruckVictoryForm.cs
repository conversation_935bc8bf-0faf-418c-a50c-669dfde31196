using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;
using UnityGameFramework.Runtime;
using Battle;
using DG.Tweening;
using Game.Hotfix.Config;
using Google.Protobuf.Collections;
using System.Linq;
using GameFramework.Event;

namespace Game.Hotfix
{
    public partial class UIBattle5V5TradeTruckVictoryForm : UGuiFormEx
    {
        private Battle5v5Component m_Battle5V5Component;
        private BattleFiled m_BattleFiled;
        private Report m_Report;
        private ulong m_AttackMax = 0;
        private ulong m_DefendMax = 0;
        private ulong m_BuffMax = 0;
        private ulong m_DebuffMax = 0;
        private bool m_HasShowAtk = false;
        private bool m_HasShowDef = false;
        bool isGotoTech;
        List<int> techs = new() { 10002, 10005 };
        Dictionary<int, string> techIcon = new()
        {
            { 10002, "Sprite/ui_maoyi/maoyi_kapian_icon2_1.png" },
            { 10005, "Sprite/ui_maoyi/maoyi_kapian_icon2_1.png" },
        };

        protected override void OnInit(object userData)
        {
            base.OnInit(userData);

            InitBind();
            InitToggle();
            HideDefault();
        }

        protected override void OnOpen(object userData)
        {
            base.OnOpen(userData);

            m_HasShowAtk = false;
            m_HasShowDef = false;
            isGotoTech = false;

            m_togReward.isOn = true;

            m_Battle5V5Component = GameEntry.Battle5v5;
            m_BattleFiled = m_Battle5V5Component.BattleFiled;
            m_Report = m_BattleFiled.RecordCtrl.Report;

            foreach (var item in m_Report.Stats.Attacker)
            {
                m_AttackMax = (ulong)Mathf.Max(m_AttackMax, item.Attack);
                m_DefendMax = (ulong)Mathf.Max(m_DefendMax, item.Defend);
                m_BuffMax = (ulong)Mathf.Max(m_BuffMax, item.Buff);
                m_DebuffMax = (ulong)Mathf.Max(m_DebuffMax, item.Debuff);
            }

            if (GameEntry.TradeTruckData.TradeVanRobResp != null)
            {
                RefreshReward(GameEntry.TradeTruckData.TradeVanRobResp.Article.ToList());
            }

            RefreshTech();
        }

        protected override void OnClose(bool isShutdown, object userData)
        {
            base.OnClose(isShutdown, userData);
        }

        public override void OnRefresh(object userData)
        {
            base.OnRefresh(userData);
        }

        private void OnBtnBackClick()
        {
            Close();
            GameEntry.LogicData.Battle5v5Data.GoBackWorldMap();
        }

        private void OnBtnShareClick()
        {

        }

        void HideDefault()
        {
            m_scrollviewHeroData.gameObject.SetActive(false);
            m_transHeroDataItem.gameObject.SetActive(false);
            m_scrollviewHeroDataDef.gameObject.SetActive(false);
            m_transHeroDataItemDef.gameObject.SetActive(false);
            m_transStrengthenItem.gameObject.SetActive(false);
        }

        void InitToggle()
        {
            m_togReward.onValueChanged.AddListener((isOn) =>
            {
                m_scrollviewReward.gameObject.SetActive(isOn);
                m_scrollviewStrengthen.gameObject.SetActive(isOn);
                m_scrollviewHeroData.gameObject.SetActive(!isOn);
                m_scrollviewHeroDataDef.gameObject.SetActive(!isOn);
            });

            m_togOutput.onValueChanged.AddListener((isOn) =>
            {
                m_scrollviewReward.gameObject.SetActive(!isOn);
                m_scrollviewStrengthen.gameObject.SetActive(!isOn);
                m_scrollviewHeroData.gameObject.SetActive(isOn);
                m_scrollviewHeroDataDef.gameObject.SetActive(!isOn);

                if (!m_HasShowAtk && isOn)
                {
                    m_HasShowAtk = true;
                    RefreshHeroItem(m_transContentHeroData, m_transHeroDataItem, m_Report.Stats.Attacker,
                        m_Report.Attacker,
                        true);
                }

            });

            m_togBear.onValueChanged.AddListener((isOn) =>
            {
                m_scrollviewReward.gameObject.SetActive(!isOn);
                m_scrollviewStrengthen.gameObject.SetActive(!isOn);
                m_scrollviewHeroData.gameObject.SetActive(!isOn);
                m_scrollviewHeroDataDef.gameObject.SetActive(isOn);

                if (!m_HasShowDef && isOn)
                {
                    m_HasShowDef = true;
                    RefreshHeroItem(m_transContentHeroDataDef, m_transHeroDataItemDef, m_Report.Stats.Attacker,
                        m_Report.Attacker, false);
                }
            });
        }

        void RefreshReward(List<Article.Article> rewards)
        {
            foreach (Transform item in m_transContentReward)
            {
                item.gameObject.SetActive(false);
            }

            for (int i = 0; i < rewards.Count; i++)
            {
                if (i < m_transContentReward.childCount)
                {
                    m_transContentReward.GetChild(i).gameObject.SetActive(true);
                    UIItemModule uiItemModule = m_transContentReward.GetChild(i).GetChild(0).GetComponent<UIItemModule>();
                    uiItemModule.SetData((itemid)rewards[i].Code, rewards[i].Amount);
                    uiItemModule.InitConfigData();
                    uiItemModule.DisplayInfo();
                }
                else
                {
                    Transform item = Instantiate(m_transRewardItem, m_transContentReward);
                    BagManager.CreatItem(item, (itemid)rewards[i].Code, rewards[i].Amount, (item) =>
                    {
                        item.GetComponent<UIButton>().useTween = false;
                        item.SetClick(item.OpenTips);
                        item.SetScale(0.75f);
                    });
                }
            }
        }

        void RefreshHeroItem(Transform parent, Transform itemTemplate, RepeatedField<BattleTeamStats> battleTeamStats,
            Battle.Team team, bool isAtk)
        {
            for (int i = 0; i <= 4; i++)
            {
                Transform item;
                if (i < parent.childCount)
                {
                    item = parent.GetChild(i);
                }
                else
                {
                    item = Instantiate(itemTemplate, parent);
                }

                if (i < battleTeamStats.Count && i < team.Heroes.Count)
                {
                    BattleTeamStats teamStats = battleTeamStats[i];
                    item.gameObject.SetActive(true);
                    RefreshItem(teamStats, item, team.Heroes[i], isAtk);
                }
                else
                {
                    item.gameObject.SetActive(false);
                }
            }
        }

        private void RefreshItem(BattleTeamStats teamStats, Transform item, Battle.TeamHero teamHero, bool isAtk)
        {
            var heroItem = item.Find("UIHeroItem")?.GetComponent<UIHeroItem>();
            var txtValue = item.Find("value")?.GetComponent<UIText>();
            var txtName = item.Find("name")?.GetComponent<UIText>();
            var sliderValue = item.Find("slider")?.GetComponent<Slider>();

            HeroModule heroModule = GameEntry.LogicData.HeroData.GetHeroModule((itemid)teamHero.Code);
            heroItem.Refresh(heroModule);
            heroItem.HideTeamBg();
            txtName.text = heroModule.Name;

            var value = isAtk ? teamStats.Attack : teamStats.Defend;
            var valueMax = isAtk ? m_AttackMax : m_DefendMax;

            DOTween.To(() => 0, newValue =>
            {
                txtValue.text = ToolScriptExtend.FormatNumberWithUnit(value * newValue);
                sliderValue.value = value * newValue / valueMax;
            }, 1f, 1f);
        }

        void RefreshTech()
        {
            for (int i = 0; i < techs.Count; i++)
            {
                Transform item;
                if (i < m_transContentStrengthen.childCount)
                {
                    item = m_transContentStrengthen.GetChild(i);
                    item.gameObject.SetActive(true);
                }
                else
                {
                    item = Instantiate(m_transStrengthenItem, m_transContentStrengthen);
                    item.gameObject.SetActive(true);
                }

                int techId = techs[i];
                UIImage icon = item.Find("border/icon").GetComponent<UIImage>();
                icon.SetImage(techIcon[techId]);
                UIText txtDesc = item.Find("txtDesc").GetComponent<UIText>();
                int level = GameEntry.LogicData.TechData.GetCompeleteTechLevelByGroup(techId);
                int levelMax = GameEntry.LogicData.TechData.GetTechLevelMax(techId);
                if (level >= levelMax)
                {
                    item.gameObject.SetActive(false);
                    return;
                }
                if (level == 0) level = levelMax;
                tech_config config = GameEntry.LogicData.TechData.GetTechConfigByGroupLevel(techId, level);
                if (config == null || config.tech_attributes.Count == 0) return;
                txtDesc.text = ToolScriptExtend.GetLang(config.tech_desc) + (config.tech_attributes[0].value / 100) + "%";

                UIButton btnGo = item.Find("btnGo").GetComponent<UIButton>();
                btnGo.onClick.RemoveAllListeners();
                btnGo.onClick.AddListener(() =>
                {
                    BuildingModule buildingModule1 = GameEntry.LogicData.BuildingData.GetBuildingModuleById(2201);
                    BuildingModule buildingModule2 = GameEntry.LogicData.BuildingData.GetBuildingModuleById(2202);
                    BuildingModule buildingModule3 = GameEntry.LogicData.BuildingData.GetBuildingModuleById(2203);
                    BuildingModule buildingModule = buildingModule1 ?? buildingModule2 ?? buildingModule3;
                    if (buildingModule != null)
                    {
                        GameEntry.UI.OpenUIForm(EnumUIForm.UITechForm, new UITechFormParams()
                        {
                            buildingModule = buildingModule,
                            isGoto = true,
                            techtype = techtype.techtype_10,
                            techId = techId,
                            closeDirectly = true,
                        });
                        Close();
                        GameEntry.LogicData.Battle5v5Data.GoBackWorldMap();
                    }
                    else
                    {
                        ColorLog.Yellow("没有科技建筑");
                    }
                });
            }
        }
    }
}
